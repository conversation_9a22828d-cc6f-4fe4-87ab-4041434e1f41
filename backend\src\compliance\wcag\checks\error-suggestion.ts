/**
 * WCAG-031: Error Suggestion Check
 * Success Criterion: 3.3.3 Error Suggestion (Level AA)
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { FormAccessibilityAnalyzer } from '../utils/form-accessibility-analyzer';
import { WcagEvidence } from '../types';
import { WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';

interface ErrorSuggestionAnalysis {
  formElements: Array<{
    element: string;
    type: string;
    selector: string;
    hasValidation: boolean;
    hasErrorMessage: boolean;
    hasSuggestion: boolean;
    errorMessageText?: string;
    suggestionText?: string;
    validationPattern?: string;
    isRequired: boolean;
    hasAriaDescribedby: boolean;
    hasAriaInvalid: boolean;
  }>;
  errorContainers: Array<{
    selector: string;
    text: string;
    isVisible: boolean;
    associatedField?: string;
    hasSuggestion: boolean;
    suggestionQuality: 'none' | 'poor' | 'good' | 'excellent';
  }>;
  totalFormElements: number;
  elementsWithValidation: number;
  elementsWithSuggestions: number;
  elementsWithPoorSuggestions: number;
}

export interface ErrorSuggestionConfig extends EnhancedCheckConfig {
  enableFormAccessibilityAnalysis?: boolean;
  enableAISemanticValidation?: boolean;
  enableAccessibilityPatterns?: boolean;
}

export class ErrorSuggestionCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private formAccessibilityAnalyzer = FormAccessibilityAnalyzer.getInstance();

  // Patterns that indicate good error suggestions
  private readonly goodSuggestionPatterns = [
    /please enter a valid/i,
    /format should be/i,
    /example:/i,
    /try:/i,
    /should contain/i,
    /must include/i,
    /use the format/i,
    /enter.*like/i,
    /should be.*characters/i,
    /password must/i,
  ];

  // Patterns that indicate poor error suggestions
  private readonly poorSuggestionPatterns = [
    /^invalid$/i,
    /^error$/i,
    /^wrong$/i,
    /^incorrect$/i,
    /^required$/i,
    /^field is required$/i,
    /^this field/i,
    /^please fix/i,
  ];

  async performCheck(config: ErrorSuggestionConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: ErrorSuggestionConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 5000,
      },
      enableFormAccessibilityAnalysis: true,
      enableAISemanticValidation: true,
      enableAccessibilityPatterns: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-031',
      'Error Suggestion',
      'understandable',
      0.0815,
      'AA',
      enhancedConfig,
      this.executeErrorSuggestionCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with error suggestion analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-031',
        ruleName: 'Error Suggestion',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.8,
          checkType: 'error-suggestion-analysis',
          formValidationAnalysis: true,
          suggestionQualityAssessment: true,
          formAccessibilityAnalysis: enhancedConfig.enableFormAccessibilityAnalysis,
          aiSemanticValidation: enhancedConfig.enableAISemanticValidation,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.8,
        maxEvidenceItems: 35,
      },
    );
    const totalElements = enhancedEvidence.reduce((sum, ev) => sum + (ev.elementCount || 0), 0);
    const failedElements = enhancedEvidence.filter((ev) => ev.severity === 'error').length;

    return {
      ...result,
      evidence: enhancedEvidence,
      elementCounts: {
        total: totalElements,
        failed: failedElements,
        passed: totalElements - failedElements,
      },
      performance: {
        scanDuration: result.executionTime,
        elementsAnalyzed: totalElements,
      },
      checkMetadata: {
        version: '1.0.0',
        algorithm: 'error-suggestion-analysis',
        confidence: 0.75,
        additionalData: {
          checkType: 'form-validation',
          automationLevel: 'high',
        },
      },
    };
  }

  private async executeErrorSuggestionCheck(
    page: Page,
    _config: ErrorSuggestionConfig,
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidence[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Enhanced form accessibility analysis using FormAccessibilityAnalyzer
    const formAccessibilityReport = await this.formAccessibilityAnalyzer.analyzeFormAccessibility(
      page,
      {
        analyzeLabels: true,
        analyzeValidation: true,
        analyzeErrorHandling: true,
        analyzeKeyboardAccess: true,
        strictMode: true,
      },
    );

    // Analyze error suggestions using enhanced analyzer
    const errorSuggestionAnalysis = await this.analyzeErrorSuggestionsEnhanced(
      page,
      formAccessibilityReport as any,
    );

    // Analyze suggestion quality using AI semantic validation
    const suggestionQualityAnalysis = await this.analyzeSuggestionQuality(page);

    // Analyze error recovery mechanisms
    const errorRecoveryAnalysis = await this.analyzeErrorRecoveryMechanisms(page);

    // Combine analysis results
    const allAnalyses = [errorSuggestionAnalysis, suggestionQualityAnalysis, errorRecoveryAnalysis];
    let totalChecks = 0;
    let passedChecks = 0;

    allAnalyses.forEach((analysis) => {
      totalChecks += analysis.totalChecks;
      passedChecks += analysis.passedChecks;
      evidence.push(...analysis.evidence);
      issues.push(...analysis.issues);
      recommendations.push(...analysis.recommendations);
    });

    // Calculate score
    const score = totalChecks > 0 ? Math.round((passedChecks / totalChecks) * 100) : 100;

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Enhanced error suggestions analysis using FormAccessibilityAnalyzer
   */
  private async analyzeErrorSuggestionsEnhanced(
    page: Page,
    formAccessibilityReport: {
      forms: Array<{
        fields: Array<{
          selector: string;
          hasValidation: boolean;
          hasAccessibleErrors: boolean;
          hasSuggestions: boolean;
        }>;
      }>;
    },
  ) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    let totalChecks = 0;
    let passedChecks = 0;

    (
      formAccessibilityReport as { forms: Array<{ fields: Array<Record<string, unknown>> }> }
    ).forms.forEach((form: { fields: Array<Record<string, unknown>> }, formIndex: number) => {
      form.fields.forEach((field: Record<string, unknown>, fieldIndex: number) => {
        // Only check fields that have validation
        if ((field as any).hasValidation) {
          totalChecks++;

          // Check if field has error suggestions
          if ((field as any).hasAccessibleErrors && (field as any).hasSuggestions) {
            passedChecks++;
            evidence.push({
              type: 'text',
              description: `Field ${fieldIndex + 1} in form ${formIndex + 1} has error suggestions`,
              value: `${(field as any).selector} - has accessible errors and suggestions`,
              selector: (field as any).selector,
              severity: 'info',
            });
          } else {
            issues.push(`Field ${fieldIndex + 1} in form ${formIndex + 1} lacks error suggestions`);
            evidence.push({
              type: 'code',
              description: `Field ${fieldIndex + 1} requires error suggestions`,
              value: `${(field as any).selector} - hasAccessibleErrors: ${(field as any).hasAccessibleErrors}, hasSuggestions: ${(field as any).hasSuggestions}`,
              selector: (field as any).selector,
              severity: 'error',
            });
            recommendations.push(
              `Add helpful error suggestions to field ${fieldIndex + 1} in form ${formIndex + 1}`,
            );
          }
        }
      });
    });

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Analyze suggestion quality using AI semantic validation
   */
  private async analyzeSuggestionQuality(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Find error suggestion elements
    const suggestions = await page.$$eval(
      '.error-suggestion, .help-text, .validation-message, [aria-describedby*="error"], [aria-describedby*="help"]',
      (elements) => {
        return elements.map((element, index) => {
          const text = element.textContent?.trim() || '';
          const isVisible = (element as HTMLElement).offsetParent !== null;
          const associatedField =
            element.getAttribute('aria-describedby') ||
            element.closest('form')?.querySelector(`[aria-describedby="${element.id}"]`);

          // Analyze suggestion quality
          const isHelpful =
            text.length > 20 &&
            (text.includes('should') ||
              text.includes('must') ||
              text.includes('format') ||
              text.includes('example') ||
              text.includes('try') ||
              text.includes('correct'));

          const isSpecific =
            !text.toLowerCase().includes('invalid') ||
            text.includes('format') ||
            text.includes('example');

          return {
            index,
            text,
            isVisible,
            hasAssociatedField: !!associatedField,
            selector: `suggestion-${index}`,
            isEmpty: text.length === 0,
            isHelpful,
            isSpecific,
            quality: isHelpful && isSpecific ? 'good' : isHelpful ? 'fair' : 'poor',
          };
        });
      },
    );

    const totalChecks = suggestions.length;
    let passedChecks = 0;

    suggestions.forEach((suggestion, index) => {
      let suggestionPassed = true;

      // Check if suggestion is visible and has content
      if (!suggestion.isVisible || suggestion.isEmpty) {
        suggestionPassed = false;
        issues.push(`Error suggestion ${index + 1} is not visible or empty`);
        evidence.push({
          type: 'code',
          description: `Error suggestion ${index + 1} visibility issue`,
          value: `visible: ${suggestion.isVisible}, hasContent: ${!suggestion.isEmpty}`,
          severity: 'error',
        });
        recommendations.push(
          `Ensure error suggestion ${index + 1} is visible and has meaningful content`,
        );
      }

      // Check suggestion quality
      if (suggestion.quality === 'poor') {
        suggestionPassed = false;
        issues.push(`Error suggestion ${index + 1} is not helpful`);
        evidence.push({
          type: 'code',
          description: `Error suggestion ${index + 1} needs improvement`,
          value: `suggestion: "${suggestion.text}" - quality: ${suggestion.quality}`,
          severity: 'warning',
        });
        recommendations.push(
          `Improve the helpfulness and specificity of error suggestion ${index + 1}`,
        );
      }

      if (suggestionPassed) {
        passedChecks++;
        evidence.push({
          type: 'text',
          description: `Error suggestion ${index + 1} is helpful and specific`,
          value: `"${suggestion.text}" - quality: ${suggestion.quality}`,
          severity: 'info',
        });
      }
    });

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Analyze error recovery mechanisms
   */
  private async analyzeErrorRecoveryMechanisms(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check for error recovery features
    const recoveryFeatures = await page.$$eval('form', (forms) => {
      return forms.map((form, index) => {
        const hasUndo = form.querySelector('[type="reset"], .undo, .cancel') !== null;
        const hasAutoSave =
          form.hasAttribute('data-autosave') || form.querySelector('[data-autosave]') !== null;
        const hasConfirmation =
          form.querySelector('.confirm, .confirmation') !== null ||
          form.hasAttribute('data-confirm');
        const hasValidationSummary =
          form.querySelector('.error-summary, .validation-summary') !== null;

        return {
          index,
          selector: `form:nth-of-type(${index + 1})`,
          hasUndo,
          hasAutoSave,
          hasConfirmation,
          hasValidationSummary,
          recoveryScore: [hasUndo, hasAutoSave, hasConfirmation, hasValidationSummary].filter(
            Boolean,
          ).length,
        };
      });
    });

    const totalChecks = recoveryFeatures.length;
    let passedChecks = 0;

    recoveryFeatures.forEach((form, index) => {
      // Forms with good recovery mechanisms (at least 2 features)
      if (form.recoveryScore >= 2) {
        passedChecks++;
        evidence.push({
          type: 'text',
          description: `Form ${index + 1} has good error recovery mechanisms`,
          value: `${form.selector} - recovery features: ${form.recoveryScore}/4`,
          selector: form.selector,
          severity: 'info',
        });
      } else {
        issues.push(`Form ${index + 1} lacks adequate error recovery mechanisms`);
        evidence.push({
          type: 'code',
          description: `Form ${index + 1} needs better error recovery`,
          value: `${form.selector} - recovery features: ${form.recoveryScore}/4 (undo: ${form.hasUndo}, autosave: ${form.hasAutoSave}, confirmation: ${form.hasConfirmation}, summary: ${form.hasValidationSummary})`,
          selector: form.selector,
          severity: 'warning',
        });
        recommendations.push(
          `Add error recovery mechanisms to form ${index + 1} (undo, autosave, confirmation, or validation summary)`,
        );
      }
    });

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  private generateBeforeExample(element: ErrorSuggestionAnalysis['formElements'][0]): string {
    if (element.type === 'email') {
      return `<input type="email" required>\n<div class="error">Invalid email</div>`;
    } else if (element.type === 'password') {
      return `<input type="password" required>\n<div class="error">Password is required</div>`;
    } else if (element.type === 'tel') {
      return `<input type="tel" required>\n<div class="error">Invalid phone number</div>`;
    } else {
      return `<input type="${element.type}" required>\n<div class="error">Invalid input</div>`;
    }
  }

  private generateAfterExample(element: ErrorSuggestionAnalysis['formElements'][0]): string {
    if (element.type === 'email') {
      return `<input type="email" required aria-describedby="email-error">
<div id="email-error" class="error">Please enter a valid email address (e.g., <EMAIL>)</div>`;
    } else if (element.type === 'password') {
      return `<input type="password" required aria-describedby="password-error">
<div id="password-error" class="error">Password must be at least 8 characters with uppercase, lowercase, and numbers</div>`;
    } else if (element.type === 'tel') {
      return `<input type="tel" required aria-describedby="phone-error">
<div id="phone-error" class="error">Please enter a valid phone number (e.g., (*************)</div>`;
    } else {
      return `<input type="${element.type}" required aria-describedby="field-error">
<div id="field-error" class="error">Please enter a valid ${element.type}. Check the format and try again.</div>`;
    }
  }
}
